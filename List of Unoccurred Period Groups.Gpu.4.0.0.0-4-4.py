import pandas as pd
import numpy as np
try:
    import cupy as cp
    import cudf
    GPU_AVAILABLE = True
    print("GPU acceleration enabled (CuPy + cuDF)")
except ImportError:
    print("Warning: GPU libraries not installed, fallback to CPU mode")
    print("To enable GPU acceleration, install: pip install cupy-cuda11x cudf")
    import numpy as cp  # fallback to numpy
    GPU_AVAILABLE = False

from collections import Counter
from itertools import combinations
import argparse
import re
import time

# 定義顏色常量
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 結束顏色

def setup_gpu_config():
    """
    配置GPU设置和内存池
    """
    if GPU_AVAILABLE:
        try:
            # 获取GPU信息
            device_count = cp.cuda.runtime.getDeviceCount()
            print(f"检测到 {device_count} 个GPU设备")
            
            # 设置GPU设备（使用第一个GPU）
            cp.cuda.Device(0).use()
            
            # 配置内存池
            mempool = cp.get_default_memory_pool()
            pinned_mempool = cp.get_default_pinned_memory_pool()
            
            print(f"GPU加速已就绪 - 设备: {cp.cuda.runtime.getDeviceProperties(0)['name'].decode()}")
            print(f"GPU内存: {cp.cuda.runtime.memGetInfo()[1] / 1024**3:.1f} GB")
            
            return True
        except Exception as e:
            print(f"GPU初始化失败: {e}")
            return False
    return False

def load_data_optimized(file_path):
    """
    优化的数据加载函数，支持GPU加速和多种编码格式
    """
    print("Loading data...")
    start_time = time.time()
    
    # 常见的编码格式列表
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
    
    df = None
    successful_encoding = None
    
    for encoding in encodings:
        try:
            print("Attempting encoding: {}".format(encoding))
            
            if GPU_AVAILABLE:
                try:
                    # 使用cuDF加载数据（GPU加速的pandas）
                    df = cudf.read_csv(file_path, encoding=encoding)
                    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
                    print("Data loaded to GPU memory (cuDF) - encoding: {}".format(encoding))
                    successful_encoding = encoding
                    break
                except Exception as e:
                    print("cuDF loading failed ({}), trying pandas: {}".format(encoding, e))
                    # cuDF失败，尝试pandas
                    df = pd.read_csv(file_path, encoding=encoding)
                    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
                    print(f"数据加载成功 (pandas) - 编码: {encoding}")
                    successful_encoding = encoding
                    break
            else:
                df = pd.read_csv(file_path, encoding=encoding)
                df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
                print(f"Data loaded successfully (pandas) - Encoding: {encoding}")
                successful_encoding = encoding
                break
                
        except UnicodeDecodeError as e:
            print(f"Encoding {encoding} failed: {e}")
            continue
        except Exception as e:
            print(f"Load failed ({encoding}): {e}")
            continue
    
    if df is None:
        # 如果所有编码都失败，尝试自动检测
        print("所有预设编码失败，尝试自动检测编码...")
        try:
            import chardet
            
            # 读取文件的前几行来检测编码
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
                confidence = result['confidence']
                
            print(f"检测到编码: {detected_encoding} (置信度: {confidence:.2f})")
            
            if detected_encoding:
                df = pd.read_csv(file_path, encoding=detected_encoding)
                df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
                successful_encoding = detected_encoding
                print(f"使用检测编码加载成功: {detected_encoding}")
                
        except ImportError:
            print("未安装chardet库，无法自动检测编码")
            print("建议安装: pip install chardet")
        except Exception as e:
            print(f"自动检测编码失败: {e}")
    
    if df is None:
        raise ValueError("无法加载 CSV 文件，请检查文件编码格式")
    
    load_time = time.time() - start_time
    print(f"数据加载完成，耗时: {load_time:.2f}秒，数据行数: {len(df)}")
    print(f"最终使用编码: {successful_encoding}")
    
    return df

def gpu_array_operations(numbers_array, operation='mean'):
    """
    GPU加速的数组操作
    """
    if GPU_AVAILABLE:
        try:
            # 转换到GPU
            gpu_array = cp.asarray(numbers_array)
            
            if operation == 'mean':
                result = float(cp.mean(gpu_array))
            elif operation == 'max':
                result = int(cp.max(gpu_array))
            elif operation == 'counter':
                # GPU加速的计数操作
                unique, counts = cp.unique(gpu_array, return_counts=True)
                result = dict(zip(unique.get(), counts.get()))
            else:
                result = gpu_array.get()  # 回到CPU
            
            return result
        except Exception as e:
            print(f"GPU操作失败，回退到CPU: {e}")
            
    # CPU回退
    if operation == 'mean':
        return float(np.mean(numbers_array))
    elif operation == 'max':
        return int(np.max(numbers_array))
    elif operation == 'counter':
        return Counter(numbers_array)
    else:
        return numbers_array

def analyze_number_streaks():
    """
    分析每个号码未开出的期数，追踪每个号码自上次开出以来经过了多少期
    """
    # GPU初始化
    gpu_enabled = setup_gpu_config()
    
    # 优化的数据加载
    df = load_data_optimized('data/2015-2025.csv')
    
    print('=== 号码未开出期数统计分析 (GPU加速版本) ===')
    print(f"GPU加速状态: {'启用' if gpu_enabled else '禁用'}")
    
    # 转换期号为整数并排序
    print("正在处理数据...")
    if GPU_AVAILABLE and hasattr(df, 'to_pandas'):
        # cuDF转换
        df['period'] = df['period'].astype('int32')
        df = df.sort_values('period')
        df_pandas = df.to_pandas()  # 保留一份pandas版本用于兼容性
    else:
        # 标准pandas
        df['period'] = df['period'].astype(int)
        df = df.sort_values('period')
        df_pandas = df
    
    # 创建号码列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化追踪字典，记录每个号码当前的未出现期数
    number_streaks = {}    # 记录每个号码当前的未出现期数
    
    # 初始化所有号码
    for i in range(1, 50):
        number_streaks[i] = 0
    
    # GPU加速的数组操作准备
    if GPU_AVAILABLE:
        # 预分配GPU内存用于加速计算
        gpu_number_range = cp.arange(1, 50, dtype=cp.int32)
        print("GPU内存预分配完成")
    
    # 按期分析 - GPU加速版本
    streak_analysis = []
    analysis_start_time = time.time()
    
    print(f"开始分析 {len(df_pandas)} 期数据...")
    
    for i, row in df_pandas.iterrows():
        current_period = row['period']

        # 获取当期开出的号码
        current_numbers = [row[col] for col in number_columns]

        # 记录本期各号码的未出现期数（在更新之前记录）
        streak_record = {
            '期号': current_period,
            '日期': row['date'],
        }

        # 添加每个号码的未出现期数（记录开奖前的状态）
        for num in range(1, 50):
            streak_record[f'号码{num}'] = number_streaks[num]

        # GPU加速的号码状态更新
        if GPU_AVAILABLE:
            try:
                # 使用GPU向量化操作更新号码状态
                current_numbers_gpu = cp.array(current_numbers, dtype=cp.int32)
                all_numbers_gpu = cp.arange(1, 50, dtype=cp.int32)
                
                # 创建掩码：哪些号码在本期开出
                is_drawn_mask = cp.isin(all_numbers_gpu, current_numbers_gpu)
                
                # 向量化更新：开出的号码重置为0，未开出的+1
                streaks_array = cp.array(list(number_streaks.values()), dtype=cp.int32)
                streaks_array = cp.where(is_drawn_mask, 0, streaks_array + 1)
                
                # 更新字典
                streaks_list = streaks_array.get().tolist()
                for idx, num in enumerate(range(1, 50)):
                    number_streaks[num] = streaks_list[idx]
                    
            except Exception as e:
                # 回退到CPU版本
                for num in range(1, 50):
                    if num in current_numbers:
                        number_streaks[num] = 0
                    else:
                        number_streaks[num] += 1
        else:
            # CPU版本的更新
            for num in range(1, 50):
                if num in current_numbers:
                    number_streaks[num] = 0
                else:
                    number_streaks[num] += 1
            
        # 使用GPU加速计算统计信息
        streaks_values = list(number_streaks.values())
        max_streak = gpu_array_operations(streaks_values, 'max')
        avg_streak = gpu_array_operations(streaks_values, 'mean')
        
        streak_record['最大未出现期数'] = max_streak
        streak_record['平均未出现期数'] = avg_streak
        streak_record['开出号码'] = current_numbers
        
        streak_analysis.append(streak_record)
        
        # 显示处理进度
        if (i + 1) % 500 == 0 or i == len(df_pandas) - 1:
            elapsed = time.time() - analysis_start_time
            progress = (i + 1) / len(df_pandas) * 100
            print(f"处理进度: {progress:.1f}% ({i+1}/{len(df_pandas)}) - 耗时: {elapsed:.1f}秒")
    
    analysis_time = time.time() - analysis_start_time
    print(f"\n数据分析完成，总耗时: {analysis_time:.2f}秒")
    print(f"平均每期处理时间: {analysis_time/len(df_pandas)*1000:.2f}毫秒")
    
    # GPU加速的DataFrame创建
    if GPU_AVAILABLE:
        try:
            # 尝试使用cuDF创建DataFrame
            import cudf
            streak_df = cudf.DataFrame(streak_analysis)
            if hasattr(streak_df, 'to_pandas'):
                streak_df_pandas = streak_df.to_pandas()
            else:
                streak_df_pandas = streak_df
        except:
            streak_df_pandas = pd.DataFrame(streak_analysis)
    else:
        streak_df_pandas = pd.DataFrame(streak_analysis)
    
    # === 動態計算百分比統計數據 (GPU加速) ===
    print('\n正在計算動態百分比數據... (GPU加速)')
    calc_start_time = time.time()
    # 不再提前計算，改為每期動態計算
    
    # 显示最近几期的分析结果 (GPU加速版本)
    print('\n最近230期各号码未出现期数:')
    recent_periods = streak_df_pandas.tail(230)
    
    # GPU加速的数据处理
    recent_analysis_start = time.time()
    total_recent = len(recent_periods)
    
    for i, (_, row) in enumerate(recent_periods.iterrows()):
        current_period_index = streak_df_pandas.index[streak_df_pandas['期号'] == row['期号']].tolist()[0]
        
        # GPU加速的動態計算該期之前的百分比統計數據
        period_count_combinations = Counter()
        period_count_with_drawn = Counter()
        
        # 只使用該期之前的資料來計算百分比
        historical_data = streak_df_pandas.iloc[:current_period_index]
        
        # GPU加速的历史数据处理
        if GPU_AVAILABLE and len(historical_data) > 100:  # 只在数据量较大时使用GPU
            try:
                # 将数据移到GPU进行快速处理
                # 这里使用类似向量化的方式处理批量数据
                pass  # 详细实现留在下一步
            except Exception as e:
                pass  # 回退到CPU处理
        
        for _, hist_row in historical_data.iterrows():
            all_numbers_streaks_hist = [(num, hist_row[f'号码{num}']) for num in range(1, 50)]
            streaks_by_period_temp = {}
            for num, streak in all_numbers_streaks_hist:
                if streak not in streaks_by_period_temp:
                    streaks_by_period_temp[streak] = []
                streaks_by_period_temp[streak].append(num)

            current_numbers_hist = hist_row['开出号码']
            
            for period, numbers in streaks_by_period_temp.items():
                count = len(numbers)
                combination_key = f"[{period}期][{count}次]"
                period_count_combinations[combination_key] += 1
                
                has_drawn_number = any(num in current_numbers_hist for num in numbers)
                if has_drawn_number:
                    period_count_with_drawn[combination_key] += 1
        
        print(f"期号: {row['期号']} ({row['日期']})")
        # 获取每个开奖号码的未出现期数
        current_numbers = row['开出号码']
        streaks_for_numbers = [str(row[f'号码{num}']) + "期" for num in current_numbers]
        print(f"  开出号码: {row['开出号码']} " + ",".join(streaks_for_numbers))

        # 获取所有号码的未出现期数并分组
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(str(num))

        # 计算新的统计
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period.get(streak, []))
            new_stats.append(str(count))
        
        print(f"--- 未連續期數數量號碼統計: {','.join(new_stats)}")

        # 统计未出现期数数量的出现次数
        period_counts = [len(v) for v in streaks_by_period.values()]
        # GPU加速的计数操作
        if GPU_AVAILABLE:
            try:
                counts_counter_result = gpu_array_operations(period_counts, 'counter')
                if isinstance(counts_counter_result, dict):
                    counts_counter = Counter(counts_counter_result)
                else:
                    counts_counter = Counter(period_counts)
            except:
                counts_counter = Counter(period_counts)
        else:
            counts_counter = Counter(period_counts)
        
        sorted_counts = sorted(counts_counter.items(), key=lambda item: item[0])
        stats_str = "  ".join([f"{count} 次: {num_groups} 組" for count, num_groups in sorted_counts])
        print(f"--- 未出現期數的數量統計 ---\n  {stats_str}")
        
        # 显示处理进度
        if (i + 1) % 50 == 0 or i == total_recent - 1:
            elapsed = time.time() - recent_analysis_start
            progress = (i + 1) / total_recent * 100
            print(f"\n[处理进度: {progress:.1f}% ({i+1}/{total_recent}) - 耗时: {elapsed:.1f}秒]\n")

        # 新增的統計：按"次数"对"期数"进行分组（含動態百分比）
        counts_to_streaks = {}
        for streak, nums in streaks_by_period.items():
            count = len(nums)
            if count not in counts_to_streaks:
                counts_to_streaks[count] = []
            counts_to_streaks[count].append(int(streak))
        
        # 格式化输出並加入動態百分比
        sorted_counts_keys = sorted(counts_to_streaks.keys(), reverse=True)
        
        for count in sorted_counts_keys:
            streaks = sorted(counts_to_streaks[count])
            
            # 為每個期數計算動態百分比並加上顏色
            period_with_percentage = []
            for period in streaks:
                combo_key = f"[{period}期][{count}次]"
                if combo_key in period_count_combinations and combo_key in period_count_with_drawn:
                    total_count = period_count_combinations[combo_key]
                    with_drawn = period_count_with_drawn[combo_key]
                    if total_count > 0:
                        percentage = (with_drawn / total_count) * 100
                        
                        # 根據百分比選擇顏色
                        if percentage >= 60:
                            color = Colors.RED + Colors.BOLD
                        elif percentage >= 50:
                            color = Colors.MAGENTA
                        elif percentage >= 40:
                            color = Colors.YELLOW
                        elif percentage >= 30:
                            color = Colors.CYAN
                        else:
                            color = Colors.GREEN
                        
                        period_with_percentage.append(f"{color}{percentage:.3f}%{Colors.END} {period}期")
                    else:
                        period_with_percentage.append(f"{Colors.GREEN}0.00%{Colors.END} {period}期")
                else:
                    period_with_percentage.append(f"{Colors.WHITE}0.00%{Colors.END} {period}期")
            
            periods_str = " ".join(period_with_percentage)
            print(f"   {count}次: {periods_str}")

        print(f"  最大未出现期数: {row['最大未出现期数']}")
        print("  未出现期数分組列表: ")
        
        # 计算并显示当期总组数
        period_groups_temp = {}
        for num, streak in all_numbers_streaks:
            if streak not in period_groups_temp:
                period_groups_temp[streak] = []
            period_groups_temp[streak].append(num)
        
        current_total_groups = len(period_groups_temp)
        print(f"所有號碼的未出現期數分組:共{current_total_groups}組數")

        # 使用帶動態百分比的辅助函数打印分组，并传入当期开出的号码
        _print_streaks_by_period_with_percentage(all_numbers_streaks, period_count_combinations, period_count_with_drawn, drawn_numbers=current_numbers)

    # GPU加速的最终统计
    print("\n目前所有號碼的未出現期數分組: (GPU加速版本)")
    current_streaks_list = list(number_streaks.items())
    
    # 计算并显示总组数
    final_streaks_by_period_temp = {}
    for num, streak in current_streaks_list:
        if streak not in final_streaks_by_period_temp:
            final_streaks_by_period_temp[streak] = []
        final_streaks_by_period_temp[streak].append(num)
    
    total_groups = len(final_streaks_by_period_temp)
    print(f"所有號碼的未出現期數分組:共{total_groups}組數")
    
    # 計算最終的動態百分比統計數據（基於整個歷史資料）- GPU加速
    print("\n正在计算最终统计数据 (GPU加速)...")
    final_calc_start = time.time()
    
    final_period_count_combinations = Counter()
    final_period_count_with_drawn = Counter()
    
    # GPU加速的数据处理批次
    batch_size = 1000  # 批处理大小
    total_rows = len(streak_df_pandas)
    
    for batch_start in range(0, total_rows, batch_size):
        batch_end = min(batch_start + batch_size, total_rows)
        batch_data = streak_df_pandas.iloc[batch_start:batch_end]
        
        # 在GPU上处理批次数据
        if GPU_AVAILABLE:
            try:
                # GPU加速批量处理
                for _, row in batch_data.iterrows():
                    all_numbers_streaks_temp = [(num, row[f'号码{num}']) for num in range(1, 50)]
                    streaks_by_period_temp = {}
                    for num, streak in all_numbers_streaks_temp:
                        if streak not in streaks_by_period_temp:
                            streaks_by_period_temp[streak] = []
                        streaks_by_period_temp[streak].append(num)
            
                    current_numbers_temp = row['开出号码']
                    
                    for period, numbers in streaks_by_period_temp.items():
                        count = len(numbers)
                        combination_key = f"[{period}期][{count}次]"
                        final_period_count_combinations[combination_key] += 1
                        
                        has_drawn_number = any(num in current_numbers_temp for num in numbers)
                        if has_drawn_number:
                            final_period_count_with_drawn[combination_key] += 1
            except Exception as e:
                # 回退到CPU处理
                for _, row in batch_data.iterrows():
                    all_numbers_streaks_temp = [(num, row[f'号码{num}']) for num in range(1, 50)]
                    streaks_by_period_temp = {}
                    for num, streak in all_numbers_streaks_temp:
                        if streak not in streaks_by_period_temp:
                            streaks_by_period_temp[streak] = []
                        streaks_by_period_temp[streak].append(num)
            
                    current_numbers_temp = row['开出号码']
                    
                    for period, numbers in streaks_by_period_temp.items():
                        count = len(numbers)
                        combination_key = f"[{period}期][{count}次]"
                        final_period_count_combinations[combination_key] += 1
                        
                        has_drawn_number = any(num in current_numbers_temp for num in numbers)
                        if has_drawn_number:
                            final_period_count_with_drawn[combination_key] += 1
        else:
            # CPU处理
            for _, row in batch_data.iterrows():
                all_numbers_streaks_temp = [(num, row[f'号码{num}']) for num in range(1, 50)]
                streaks_by_period_temp = {}
                for num, streak in all_numbers_streaks_temp:
                    if streak not in streaks_by_period_temp:
                        streaks_by_period_temp[streak] = []
                    streaks_by_period_temp[streak].append(num)
        
                current_numbers_temp = row['开出号码']
                
                for period, numbers in streaks_by_period_temp.items():
                    count = len(numbers)
                    combination_key = f"[{period}期][{count}次]"
                    final_period_count_combinations[combination_key] += 1
                    
                    has_drawn_number = any(num in current_numbers_temp for num in numbers)
                    if has_drawn_number:
                        final_period_count_with_drawn[combination_key] += 1
        
        # 显示批处理进度
        progress = batch_end / total_rows * 100
        elapsed = time.time() - final_calc_start
        print(f"\r批处理进度: {progress:.1f}% ({batch_end}/{total_rows}) - 耗时: {elapsed:.1f}秒", end="")
    
    final_calc_time = time.time() - final_calc_start
    print(f"\n\n最终统计计算完成，耗时: {final_calc_time:.2f}秒")
    
    # 計算百分比並顯示
    final_streaks_by_period = {}
    for num, streak in current_streaks_list:
        if streak not in final_streaks_by_period:
            final_streaks_by_period[streak] = []
        final_streaks_by_period[streak].append(num)
    
    # 顯示帶百分比的分組
    _print_streaks_by_period_with_percentage(current_streaks_list, final_period_count_combinations, final_period_count_with_drawn, is_final_summary=True)

    # Final summary stats
    final_period_counts = [len(v) for v in final_streaks_by_period.values()]
    final_counts_counter = Counter(final_period_counts)
    
    final_sorted_counts = sorted(final_counts_counter.items(), key=lambda item: item[0])
    final_stats_str = "  ".join([f"{count} 次: {num_groups} 組" for count, num_groups in final_sorted_counts])
    print(f"--- 未出現期數的數量統計 ---\n  {final_stats_str}")
    
    # === 新增統計：所有號碼的未出現期數分組總數 ===
    total_groups = len(final_streaks_by_period)
    print(f"\n所有號碼的未出現期數分組:共{total_groups}組數")
    
    # === 新增統計：統計每期出現期數分組總數的頻率 ===
    print(f"\n統計每期出現期數分組")
    
    # 統計所有歷史記錄中的總組數分布
    total_groups_counter = Counter()
    # 新增：統計每個總組數中包含特定數字的情況
    total_groups_with_numbers = {}
    
    print("正在統計各種總組數的出現頻率...")
    
    for _, row in streak_df_pandas.iterrows():
        # 獲取該期的所有號碼未出現期數
        all_numbers_streaks_temp = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period_temp = {}
        for num, streak in all_numbers_streaks_temp:
            if streak not in streaks_by_period_temp:
                streaks_by_period_temp[streak] = []
            streaks_by_period_temp[streak].append(num)
        
        # 統計該期的總組數
        period_total_groups = len(streaks_by_period_temp)
        total_groups_counter[period_total_groups] += 1
        
        # 獲取當期開出的號碼
        current_numbers = row['开出号码']
        
        # 計算當期的“未連續期數數量號碼統計”
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period_temp.get(streak, []))
            new_stats.append(count)
        
        # 紀錄該總組數中包含特定數字的情況
        if period_total_groups not in total_groups_with_numbers:
            total_groups_with_numbers[period_total_groups] = {}
            # 初始化 1-6 的統計
            for i in range(1, 7):
                total_groups_with_numbers[period_total_groups][i] = 0
        
        # 統計本期“未連續期數數量號碼統計”中包含 1-6 的次數
        for target_number in range(1, 7):
            if target_number in new_stats:
                total_groups_with_numbers[period_total_groups][target_number] += 1
    
    # 顯示統計結果
    print(f"統計完成，分析了 {len(streak_df_pandas)} 期記錄\n")
    
    # 按組數排序顯示結果（過濾低頻數據）
    sorted_total_groups = sorted(total_groups_counter.items())
    # 設置最小顯示閨值，只顯示出現次數較多的組數
    min_occurrence_threshold = max(5, len(streak_df_pandas) * 0.005)  # 至少 5 次或總數據的 0.5%
    
    for groups_count, occurrence in sorted_total_groups:
        # 過濾低頻數數據，只顯示有統計意義的結果
        if occurrence < min_occurrence_threshold:
            continue
            
        percentage = (occurrence / len(streak_df_pandas)) * 100
        base_info = f"{groups_count}組出現{occurrence}次，出現率: {percentage:.2f}%"
        
        # 新增：顯示包含特定數字的統計（加入顏色）
        if groups_count in total_groups_with_numbers:
            details = []
            for target_num in range(1, 7):  # 顯示 1-6 的統計
                count_with_target = total_groups_with_numbers[groups_count][target_num]
                if count_with_target > 0:  # 只顯示有數值的情況
                    target_percentage = (count_with_target / occurrence) * 100 if occurrence > 0 else 0
                    
                    # 根據百分比選擇顏色
                    if target_percentage >= 80:
                        color = Colors.RED + Colors.BOLD  # 高頻率 - 紅色加粗
                    elif target_percentage >= 70:
                        color = Colors.RED  # 高頻率 - 紅色
                    elif target_percentage >= 60:
                        color = Colors.MAGENTA + Colors.BOLD  # 較高頻率 - 紫色加粗
                    elif target_percentage >= 60:
                        color = Colors.MAGENTA  # 較高頻率 - 紫色
                    elif target_percentage >= 45:
                        color = Colors.YELLOW  # 中等頻率 - 黃色
                    elif target_percentage >= 30:
                        color = Colors.CYAN  # 較低頻率 - 青色
                    else:
                        color = Colors.GREEN  # 低頻率 - 綠色
                    
                    colored_percentage = f"{color}{target_percentage:.1f}%{Colors.END}"
                    details.append(f"出現{target_num}有{count_with_target}次({colored_percentage})")
            
            if details:
                detail_info = " ".join(details)
                print(f"{base_info} {detail_info}")
            else:
                print(base_info)
        else:
            print(base_info)
    
    # === 新增统计：未出现期数的数量统计出现率 ===
    print("\n=== 未出現期數的數量統計出現率 ===\n新增統計名為 未出現期數的數量統計出現率")
    
    # 统计所有历史记录中 "未出现期数的数量统计" 的模式
    pattern_occurrence_counter = Counter()
    total_records = len(streak_df_pandas)
    
    print("正在分析历史记录中的模式...")
    
    for _, row in streak_df_pandas.iterrows():
        # 获取该期的所有号码未出现期数
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period_temp = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period_temp:
                streaks_by_period_temp[streak] = []
            streaks_by_period_temp[streak].append(num)
        
        # 统计该期的未出现期数数量分布
        period_counts = [len(v) for v in streaks_by_period_temp.values()]
        counts_counter = Counter(period_counts)
        
        # 将分布模式转换为字符串格式，用于统计
        pattern_parts = []
        for count in sorted(counts_counter.keys()):
            num_groups = counts_counter[count]
            pattern_parts.append(f"{count}次:{num_groups}組")
        
        pattern_str = " ".join(pattern_parts)
        pattern_occurrence_counter[pattern_str] += 1
    
    print(f"Analysis completed, analyzed {total_records} period records\n")

    # Statistics: Count-Group Distribution
    print("\n各次數對應組數的出現統計：")
    count_group_patterns = {}
    count_group_drawn_patterns = {}  # 新增：记录实际有号码开出的情况
    count_group_1_distributions = {}  # 新增：记录每种组数分布中"1"的数量分布
    
    for _, row in streak_df_pandas.iterrows():
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period_temp = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period_temp:
                streaks_by_period_temp[streak] = []
            streaks_by_period_temp[streak].append(num)
        
        period_counts = [len(v) for v in streaks_by_period_temp.values()]
        counts_counter = Counter(period_counts)
        
        # 获取当期开出的号码
        current_numbers = row['开出号码']
        
        # 计算当期的"未連續期數數量號碼統計"
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period_temp.get(streak, []))
            new_stats.append(count)
        
        # 记录每种"次数"的组数
        for count, num_groups in counts_counter.items():
            key = f"{count}次"
            if key not in count_group_patterns:
                count_group_patterns[key] = Counter()
                count_group_drawn_patterns[key] = Counter()
                count_group_1_distributions[key] = {}  # 初始化1的数量分布字典
            
            count_group_patterns[key][num_groups] += 1
            
            # 检查"未連續期數數量號碼統計"中是否包含该"次数"值
            has_count_in_stats = count in new_stats
            
            if has_count_in_stats:
                count_group_drawn_patterns[key][num_groups] += 1
                
                # 计算"數量號碼統計"中包含特定次数的个数（针对1次、2次、3次、4次、5次和6次）
                if count in [1, 2, 3, 4, 5, 6]:
                    # 统计"數量號碼統計"列表中包含多少个对应的数字
                    count_occurrences_in_stats = new_stats.count(count)  # 计算列表中该数字的个数
                    
                    # 记录该次数的数量分布
                    if num_groups not in count_group_1_distributions[key]:
                        count_group_1_distributions[key][num_groups] = Counter()
                    count_group_1_distributions[key][num_groups][count_occurrences_in_stats] += 1
    
    # 显示每种"次数"对应的"组数"分布（含开出统计）
    for count_key in sorted(count_group_patterns.keys(), key=lambda x: int(x.replace('次', ''))):
        group_counter = count_group_patterns[count_key]
        group_drawn_counter = count_group_drawn_patterns[count_key]
        
        # 计算该"次数"的总出现次数
        total_count_occurrences = sum(group_counter.values())
        
        # 过滤掉只出现1次的极端情况，只显示有统计意义的数据
        if total_count_occurrences <= 1:
            continue
            
        print(f"\n{count_key} 對應的組數分布：")
        
        # 按组数排序显示
        for num_groups in sorted(group_counter.keys()):
            occurrence = group_counter[num_groups]
            drawn_occurrence = group_drawn_counter.get(num_groups, 0)
            rate = (occurrence / total_count_occurrences) * 100 if total_count_occurrences > 0 else 0
            drawn_rate = (drawn_occurrence / occurrence) * 100 if occurrence > 0 else 0
            
            # 基本统计信息
            basic_info = f"  {num_groups}組: 出現 {occurrence}次, 數量號碼統計中有{count_key.replace('次', '')}的有 {drawn_occurrence}次, 出現率 {drawn_rate:.2f}%"
            
            # 为出现率添加颜色（优化高百分比区间的颜色分级）
            if drawn_rate >= 80:
                color = Colors.RED + Colors.BOLD  # 80%+ 红色加粗
            elif drawn_rate >= 70:
                color = Colors.RED  # 70-79% 红色
            elif drawn_rate >= 60:
                color = Colors.MAGENTA + Colors.BOLD  # 60-69% 紫色加粗
            elif drawn_rate >= 55:
                color = Colors.MAGENTA  # 50-59% 紫色
            elif drawn_rate >= 40:
                color = Colors.YELLOW  # 40-49% 黄色
            elif drawn_rate >= 30:
                color = Colors.CYAN  # 30-39% 青色
            elif drawn_rate >= 20:
                color = Colors.BLUE  # 20-29% 蓝色
            elif drawn_rate >= 15:
                color = Colors.GREEN + Colors.BOLD  # 15-19% 绿色加粗
            elif drawn_rate >= 10:
                color = Colors.GREEN  # 10-14% 绿色
            else:
                color = Colors.WHITE  # <10% 白色
            
            colored_rate = f"{color}{drawn_rate:.2f}%{Colors.END}"
            basic_info_colored = f"  {num_groups}組: 出現 {occurrence}次, 數量號碼統計中有{count_key.replace('次', '')}的有 {drawn_occurrence}次, 出現率 {colored_rate}"
            
            # 如果是1次、2次、3次、4次、5次或6次，增加相应的数量分布统计
            if count_key in ["1次", "2次", "3次", "4次", "5次", "6次"] and num_groups in count_group_1_distributions.get(count_key, {}):
                dist = count_group_1_distributions[count_key][num_groups]
                
                # 生成优化的分布统计（只显示次数不为0的项目，且排除0个X）
                dist_parts = []
                total_occurrences = sum(dist.values())
                
                # 获取当前次数的数字
                current_number = count_key.replace('次', '')
                
                for i in range(1, 7):  # 从1个X到6个X，排除0个X
                    count_i = dist.get(i, 0)
                    if count_i > 0:  # 只显示次数不为0的项目
                        dist_parts.append(f"{i}个{current_number}為{count_i}次")
                
                if dist_parts:  # 如果有非零项目才显示
                    dist_info = " ".join(dist_parts)
                    print(f"{basic_info_colored} {dist_info}")
                else:
                    print(basic_info_colored)  # 如果没有1个X以上的情况，只显示基本信息
                
                # 验证总数一致性（不显示验证信息，根据规范要求）
                if total_occurrences != drawn_occurrence:
                    # 只在内部记录，不显示给用户
                    pass
            else:
                print(basic_info_colored)
        
        print(f"  {count_key} 總計出現: {total_count_occurrences}次")

    # 新增的統計：按"次数"对"期数"进行分组
    counts_to_streaks = {}
    for streak, nums in final_streaks_by_period.items():
        count = len(nums)
        if count not in counts_to_streaks:
            counts_to_streaks[count] = []
        counts_to_streaks[count].append(streak)
    
    # 格式化输出
    sorted_counts_keys = sorted(counts_to_streaks.keys(), reverse=True)
    
    for count in sorted_counts_keys:
        streaks = sorted(counts_to_streaks[count])
        
        # 為每個期數計算百分比並加上顏色
        period_with_percentage = []
        for period in streaks:
            combo_key = f"[{period}期][{count}次]"
            if combo_key in final_period_count_combinations and combo_key in final_period_count_with_drawn:
                total_count = final_period_count_combinations[combo_key]
                with_drawn = final_period_count_with_drawn[combo_key]
                if total_count > 0:
                    percentage = (with_drawn / total_count) * 100
                    
                    # 根據百分比選擇顏色
                    if percentage >= 60:
                        color = Colors.RED + Colors.BOLD
                    elif percentage >= 50:
                        color = Colors.MAGENTA
                    elif percentage >= 40:
                        color = Colors.YELLOW
                    elif percentage >= 30:
                        color = Colors.CYAN
                    else:
                        color = Colors.GREEN
                    
                    period_with_percentage.append(f"{color}{percentage:.3f}%{Colors.END} {period}期")
                else:
                    period_with_percentage.append(f"{Colors.GREEN}0.00%{Colors.END} {period}期")
            else:
                period_with_percentage.append(f"{Colors.WHITE}0.00%{Colors.END} {period}期")
        
        periods_str = " ".join(period_with_percentage)
        print(f"   {count}次: {periods_str}")

    # === 新增统计：特定期数和次数组合的出现频率 ===
    print('\n=== 特定期數和次數組合統計 ===')
    
    # 顯示特定組合的統計結果
    target_combinations = ["[0期][6次]", "[1期][6次]", "[2期][6次]","[3期][6次]", "[4期][6次]",
                           "[5期][6次]", "[7期][6次]", "[8期][6次]"]
    
    print("指定組合出現次數統計:")
    total_occurrences = 0
    total_with_drawn = 0
    for combo in target_combinations:
        count = final_period_count_combinations.get(combo, 0)
        with_drawn = final_period_count_with_drawn.get(combo, 0)
        total_occurrences += count
        total_with_drawn += with_drawn
        
        # 計算百分比
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.3f}%)")
        else:
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")
    
    print(f"\n總計指定組合出現次數: {total_occurrences}次")
    print(f"總計包含圖示♨️的次數: {total_with_drawn}次")
    
    # === 統計所有 [X期][6次] 組合 ===
    print('\n=== 所有 [X期][6次] 組合統計 ===')
    six_count_combinations = {}
    six_count_with_drawn = {}
    
    # 篩選出所有 [X期][6次] 的組合
    for combo, count in final_period_count_combinations.items():
        if "[6次]" in combo:
            six_count_combinations[combo] = count
            six_count_with_drawn[combo] = final_period_count_with_drawn.get(combo, 0)
    
    # 按期數排序（提取期數進行排序）
    def extract_period(combo_str):
        # 從 "[X期][6次]" 中提取 X
        import re
        match = re.search(r'\[(\d+)期\]', combo_str)
        return int(match.group(1)) if match else 0
    
    sorted_six_combinations = sorted(six_count_combinations.items(), key=lambda x: extract_period(x[0]))
    
    total_six_occurrences = 0
    total_six_with_drawn = 0
    
    print("所有 [X期][6次] 組合出現次數統計:")
    for combo, count in sorted_six_combinations:
        with_drawn = six_count_with_drawn[combo]
        total_six_occurrences += count
        total_six_with_drawn += with_drawn
        
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.3f}%)")
        else:
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")
    
    print(f"\n[6次] 組合總計:")
    print(f"  總共有 {len(sorted_six_combinations)} 種不同期數的 [X期][6次] 組合")
    print(f"  總出現次數: {total_six_occurrences}次")
    print(f"  總包含圖示♨️ 次數: {total_six_with_drawn}次")
    if total_six_occurrences > 0:
        overall_percentage = (total_six_with_drawn / total_six_occurrences) * 100
        print(f"  整體圖示♨️ 比例: {overall_percentage:.3f}%")

    # 顯示所有組合的前20名
    print('\n所有期數次數組合統計 (前20名):')
    most_common_combinations = final_period_count_combinations.most_common(20)
    for i, (combo, count) in enumerate(most_common_combinations):
        with_drawn = final_period_count_with_drawn.get(combo, 0)
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {i+1:2d}. {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.3f}%)")
        else:
            print(f"  {i+1:2d}. {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")

    # === 新增统计：未连续期数数量号码统计的最常见组合 ===
    print('\n=== 未連續期數數量號碼統計 三個數字的組合 (前十名) ===')
    three_group_combinations_counter = Counter()
    two_group_combinations_counter = Counter()

    for _, row in streak_df_pandas.iterrows():
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(str(num))

        current_numbers = row['开出号码']
        
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period.get(streak, []))
            new_stats.append(count)
        
        new_stats.sort()
        
        # Generate all 3-number combinations from the 6 stats
        combos_3 = combinations(new_stats, 3)
        for combo in combos_3:
            three_group_combinations_counter[combo] += 1
            
        # Generate all 2-number combinations from the 6 stats
        combos_2 = combinations(new_stats, 2)
        for combo in combos_2:
            two_group_combinations_counter[combo] += 1

    # Print 3-number combos
    most_common_groups_3 = three_group_combinations_counter.most_common(10)
    for i, (group, count) in enumerate(most_common_groups_3):
        group_str = ", ".join(map(str, group))
        print(f"  {i+1:2d}. 組合: [{group_str}] - 出現次數: {count}次")

    # Print 2-number combos
    print('\n=== 未連續期數數量號碼統計 兩個數字的組合 (前十名) ===')
    most_common_groups_2 = two_group_combinations_counter.most_common(10)
    for i, (group, count) in enumerate(most_common_groups_2):
        group_str = ", ".join(map(str, group))
        print(f"  {i+1:2d}. 組合: [{group_str}] - 出現次數: {count}次")

    # 导出详细数据到CSV
    # export_columns = ['期号', '日期', '开出号码', '最大未出现期数', '平均未出现期数']
    # for num in range(1, 50):
    #     export_columns.append(f'号码{num}')
    
    # export_df = streak_df_pandas[export_columns].copy()
    # export_df['开出号码'] = export_df['开出号码'].apply(lambda x: ','.join(map(str, x)))
    
    # export_df.to_csv('number_streaks_analysis_fixed.csv', index=False, encoding='utf-8-sig')
    # print('\n详细分析结果已导出至 number_streaks_analysis_fixed.csv')
    
    # return streak_df_pandas


def _print_streaks_by_period_with_percentage(streaks_list, period_count_combinations, period_count_with_drawn, drawn_numbers=None, is_final_summary=False):
    """
    帶百分比顯示的分組函數
    """
    if drawn_numbers is None:
        drawn_numbers = []

    streaks_by_period = {}
    for num, streak in streaks_list:
        if streak not in streaks_by_period:
            streaks_by_period[streak] = []
        streaks_by_period[streak].append(num)

    sorted_streaks_keys = sorted(streaks_by_period.keys(), reverse=True)
    
    lines_to_print = []
    for streak in sorted_streaks_keys:
        numbers = streaks_by_period[streak]
        numbers.sort()
        
        # 計算百分比
        count = len(numbers)
        combo_key = f"[{streak}期][{count}次]"
        total_occurrences = period_count_combinations.get(combo_key, 0)
        with_drawn = period_count_with_drawn.get(combo_key, 0)
        
        if total_occurrences > 0:
            percentage = (with_drawn / total_occurrences) * 100
            
            # 根據百分比選擇顏色
            if percentage >= 60:
                color = Colors.RED + Colors.BOLD
            elif percentage >= 50:
                color = Colors.MAGENTA
            elif percentage >= 40:
                color = Colors.YELLOW
            elif percentage >= 30:
                color = Colors.CYAN
            else:
                color = Colors.GREEN
            
            percentage_str = f"{color}{percentage:.3f}%{Colors.END}"
        else:
            percentage = 0.0
            percentage_str = f"{Colors.GREEN}0.00%{Colors.END}"

        # Format numbers with fire emoji if they were drawn
        formatted_numbers = []
        for num in numbers:
            if num in drawn_numbers and not is_final_summary:
                formatted_numbers.append(f"♨️ {num}")
            else:
                formatted_numbers.append(str(num))
        numbers_str = ", ".join(formatted_numbers)

        # 調整格式化字符串，加入百分比
        line = f"{percentage_str} [{streak:2d}期] [{count:2d}次] {numbers_str}"
        lines_to_print.append(line)

    # 雙欄打印
    midpoint = (len(lines_to_print) + 1) // 2
    left_col = lines_to_print[:midpoint]
    right_col = lines_to_print[midpoint:]

    if len(left_col) > len(right_col):
        right_col.append("")

    def get_display_width(s):
        # 移除ANSI轉義序列，以便計算真實的顯示寬度
        clean_s = re.sub(r'\033\[[0-9;]*m', '', s)
        width = 0
        for char in clean_s:
            if '\u4e00' <= char <= '\u9fff' or char in ['♨️']:
                width += 2
            else:
                width += 1
        return width

    # 找到最長的左列行（基於顯示寬度）
    max_left_width = 0
    for line in left_col:
        width = get_display_width(line)
        if width > max_left_width:
            max_left_width = width
    
    # 增加一些間距
    max_left_width += 5
    
    for left, right in zip(left_col, right_col):
        # 移除右側字符串的前導空格
        right_stripped = right.lstrip()
        # 計算左側字符串的實際顯示寬度
        current_left_width = get_display_width(left)
        # 計算需要的填充量
        padding_needed = max_left_width - current_left_width
        # 創建填充
        padding = ' ' * padding_needed
        # 打印對齊的行
        print(f"{left}{padding}{right_stripped}")
    print()

def _print_streaks_by_period(streaks_list, drawn_numbers=None, is_final_summary=False):
    """
    辅助函数，用于以双栏格式打印按期数分组的号码列表。
    如果提供了drawn_numbers，则会在对应的号码旁显示火焰符号。
    如果 is_final_summary 为 True，则调整输出格式。
    """
    if drawn_numbers is None:
        drawn_numbers = []

    streaks_by_period = {}
    for num, streak in streaks_list:
        if streak not in streaks_by_period:
            streaks_by_period[streak] = []
        streaks_by_period[streak].append(num) # Store as int for comparison

    sorted_streaks_keys = sorted(streaks_by_period.keys(), reverse=True)
    
    lines_to_print = []
    for streak in sorted_streaks_keys:
        numbers = streaks_by_period[streak]
        numbers.sort()
        
        # Format numbers with fire emoji if they were drawn
        formatted_numbers = []
        for num in numbers:
            if num in drawn_numbers and not is_final_summary:
                formatted_numbers.append(f"♨️ {num}")
            else:
                formatted_numbers.append(str(num))
        numbers_str = ", ".join(formatted_numbers)

        # 调整格式化字符串以获得更好的对齐效果
        line = f"        [{streak:2d}期] [{len(numbers):2d}次] {numbers_str}"
        lines_to_print.append(line)

    # 双栏打印
    midpoint = (len(lines_to_print) + 1) // 2
    left_col = lines_to_print[:midpoint]
    right_col = lines_to_print[midpoint:]

    # 确保两列长度一致以便zip
    if len(left_col) > len(right_col):
        right_col.append("")

    # 辅助函数来计算字符串的显示宽度
    def get_display_width(s):
        width = 0
        for char in s:
            if '\u4e00' <= char <= '\u9fff' or char in ['♨️']: # Chinese characters or special symbols
                width += 2
            else:
                width += 1
        return width

    # 计算左列的最大显示宽度
    max_left_width = 0
    for line in left_col:
        width = get_display_width(line)
        if width > max_left_width:
            max_left_width = width
    
    # 增加一些额外的填充
    max_left_width += 4
    
    for left, right in zip(left_col, right_col):
        # 移除右侧列的额外缩进
        right_stripped = right.lstrip()
        current_width = get_display_width(left)
        padding = ' ' * (max_left_width - current_width)
        print(f"{left}{padding}{right_stripped}")
    print()


def analyze_drawn_number_combinations(df, target_number, top_n=5):
    """
    分析指定号码与其他号码的组合，统计最常一起出现的配对。
    """
    print(f'\n=== 与号码 {target_number} 的组合分析 ===')
    
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化配对计数器
    partner_counter = Counter()
    
    for _, row in df.iterrows():
        current_numbers = [row[col] for col in number_columns]
        
        # 检查目标号码是否在当期开出
        if target_number in current_numbers:
            # 找出所有与目标号码配对的伙伴号码
            partners = [num for num in current_numbers if num != target_number]
            for partner in partners:
                # 我们只关心伙伴号码，因为目标号码是固定的
                partner_counter[partner] += 1
                
    # 打印最常见的组合
    print(f'\n--- 与号码 {target_number} 最常一起开出的组合 (前{top_n}名) ---')
    most_common_partners = partner_counter.most_common(top_n)
    
    if not most_common_partners:
        print(f"  找不到与号码 {target_number} 的任何组合。")
        return

    for i, (partner, count) in enumerate(most_common_partners):
        # 创建有序的组合元组用于显示
        pair = tuple(sorted((target_number, partner)))
        pair_str = ", ".join(map(str, pair))
        print(f"  {i+1:2d}. 组合: ({pair_str}) - 出现次数: {count}次")



if __name__ == "__main__":
    # python "List of Unoccurred Period Groups.Gpu.********.py" --target-number 10 --top-n 7
    parser = argparse.ArgumentParser(description='乐透号码分析工具 (GPU加速版本)')
    parser.add_argument('--target-number', type=int, default=1, help='要分析其组合的目标号码 (预设: 1)')
    parser.add_argument('--top-n', type=int, default=10, help='要显示的最常见组合数量 (预设: 10)')
    parser.add_argument('--disable-gpu', action='store_true', help='禁用GPU加速，强制使用CPU')
    args = parser.parse_args()
    
    # 如果用户指定禁用GPU，修改全局变量
    if args.disable_gpu:
        print("用户指定GPU加速已禁用")
        # 直接修改模块级变量，无需global声明
        import sys
        current_module = sys.modules[__name__]
        current_module.GPU_AVAILABLE = False
    
    print("=" * 60)
    print("Lottery Analysis Tool - GPU Accelerated v3.0.0.02")
    print(f"GPU Status: {'Enabled' if GPU_AVAILABLE else 'Disabled'}")
    if GPU_AVAILABLE:
        print("Expected Performance Boost: 2-5x")
    print("=" * 60)
    
    # 开始计时
    total_start_time = time.time()
    
    streak_df = analyze_number_streaks()
    
    # 为了避免重复读取文件，我们从 streak_df 中提取所需数据
    df_for_combinations = load_data_optimized('data/2015-2025.csv')
    if hasattr(df_for_combinations, 'to_pandas'):
        df_for_combinations = df_for_combinations.to_pandas()
    df_for_combinations.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    analyze_drawn_number_combinations(df_for_combinations, target_number=args.target_number, top_n=args.top_n)
    
    # 显示总耗时
    total_time = time.time() - total_start_time
    print("\n" + "=" * 60)
    print(f"Analysis Complete! Total Time: {total_time:.2f} seconds")
    if GPU_AVAILABLE:
        print("GPU Acceleration Active - Performance Optimized!")
    print("=" * 60)